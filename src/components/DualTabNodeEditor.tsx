import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info, Lock, Edit3 } from 'lucide-react';

// Import the connection form component and step components
import { ConnectionForm } from "@/components/ConnectionForm/ConnectionForm";
import { InputsStep } from "@/components/ConnectionForm/InputsStep";
import { OutputsStep } from "@/components/ConnectionForm/OutputsStep";
import { ByProductsStep } from "@/components/ConnectionForm/ByProductsStep";
import { FinancialStep } from "@/components/ConnectionForm/FinancialStep";
import { FormData, OutputForm } from "@/components/ConnectionForm/types";
import { useConnectionFormData } from "@/hooks/useConnectionFormData";
import { useSectors } from "@/hooks/useSectors";

interface DualTabNodeEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (formData: any) => void;
  nodeId: string;
  baseScenarioData?: any; // Data from base scenario
  currentScenarioData?: any; // Current scenario data
  isNewNode?: boolean; // True if node doesn't exist in base scenario
  activityName?: string;
  technologyName?: string;
  baseScenarioName?: string; // Name of the base scenario
  currentScenarioName?: string; // Name of the current scenario
  availableNodes?: any[]; // Available nodes for source activity mapping
}

// Default form data factory function
const getDefaultFormData = (activityName: string = '', technologyName: string = ''): FormData => ({
  activity: activityName,
  technology: technologyName,
  startYear: "2000",
  endYear: "2075",
  customTechnology: "",
  customActivity: "",
  // CRITICAL FIX: Initialize arrays with one empty entry so InputsStep renders properly
  energyInputs: [{
    id: `energy-${Date.now()}`,
    source: "",
    unit: "GJ",
    cost: "",
    sec: "",
    sourceActivity: "Nil",
    technology: "Nil"
  }],
  emissions: [{
    id: `emission-${Date.now()}`,
    source: "",
    factor: "",
    unit: "kg"
  }],
  materialInputs: [{
    id: `material-${Date.now()}`,
    material: "",
    unit: "Tonnes",
    cost: "",
    smc: "",
    sourceActivity: "Nil",
    technology: "Nil"
  }],
  energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
  emission: { source: "", ef: "", unit: "kg" },
  matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
  byproductTechnology: "",
  byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
  byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
  financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
  financialEntries: {}
});

export const DualTabNodeEditor: React.FC<DualTabNodeEditorProps> = ({
  open,
  onClose,
  onSave,
  nodeId,
  baseScenarioData,
  currentScenarioData,
  isNewNode = false,
  activityName = '',
  technologyName = '',
  baseScenarioName = 'Base Scenario',
  currentScenarioName = 'Current Scenario',
  availableNodes = []
}) => {
  const [activeTab, setActiveTab] = useState<'base' | 'current'>('current');

  const [currentFormData, setCurrentFormData] = useState<FormData>(getDefaultFormData(activityName, technologyName));

  const [outputs, setOutputs] = useState<OutputForm[]>([]);
  const [technologies, setTechnologies] = useState<string[]>([technologyName || "Technology 1"]);
  const [technologyFormData, setTechnologyFormData] = useState<Record<string, FormData>>({});

  const [activeTechnology, setActiveTechnology] = useState(technologyName || "Technology 1");

  // Tab navigation state for current scenario tab (like ConnectionForm)
  const [activeFormTab, setActiveFormTab] = useState('inputs');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeOutputTab, setActiveOutputTab] = useState("output-0");

  // API data hooks - similar to ConnectionFormDialog
  const { sectors } = useSectors();
  const [resolvedSectorUuid, setResolvedSectorUuid] = useState<string | undefined>(undefined);
  const [resolvedActivityUuid, setResolvedActivityUuid] = useState<string | undefined>(undefined);

  // Use API data hooks
  const apiData = useConnectionFormData(resolvedActivityUuid, resolvedSectorUuid);

  // Resolve sector and activity UUIDs (similar to ConnectionFormDialog)
  useEffect(() => {
    if (sectors.length > 0) {
      // For now, use the first available sector (you might want to make this configurable)
      const firstSector = sectors[0];
      if (firstSector) {
        setResolvedSectorUuid(firstSector.uuid);
        console.log('DualTabNodeEditor: Using sector UUID:', firstSector.uuid);
      }
    }
  }, [sectors]);

  // Debug logging for API data
  useEffect(() => {
    console.log('DualTabNodeEditor: API data state changed');
    console.log('- resolvedActivityUuid:', resolvedActivityUuid);
    console.log('- resolvedSectorUuid:', resolvedSectorUuid);
    console.log('- technologies count:', apiData.technologies.technologies.length);
    console.log('- materials count:', apiData.materials.materials.length);
    console.log('- energies count:', apiData.energies.energies.length);
    console.log('- emissions count:', apiData.emissions.emissions.length);
    console.log('- isLoading:', apiData.isLoading);
  }, [resolvedActivityUuid, resolvedSectorUuid, apiData]);

  // Debug logging
  React.useEffect(() => {
    if (open) {
      console.log('=== DUAL TAB NODE EDITOR OPENED ===');
      console.log('- nodeId:', nodeId);
      console.log('- activityName:', activityName);
      console.log('- technologyName:', technologyName);
      console.log('- isNewNode:', isNewNode);
      console.log('- baseScenarioData:', baseScenarioData);
      console.log('- baseScenarioData type:', typeof baseScenarioData);
      console.log('- baseScenarioData keys:', baseScenarioData ? Object.keys(baseScenarioData) : 'null/undefined');
      console.log('- currentScenarioData:', currentScenarioData);
      console.log('- currentScenarioData type:', typeof currentScenarioData);
      console.log('- currentScenarioData keys:', currentScenarioData ? Object.keys(currentScenarioData) : 'null/undefined');
      console.log('=====================================');
    }
  }, [open, nodeId, activityName, technologyName, isNewNode, baseScenarioData, currentScenarioData]);

  // Initialize form data based on base scenario and current scenario
  useEffect(() => {
    if (open) {
      console.log('DualTabNodeEditor initializing form data...');
      console.log('- currentScenarioData:', currentScenarioData);
      console.log('- baseScenarioData:', baseScenarioData);
      console.log('- isNewNode:', isNewNode);

      // CRITICAL FIX: Always initialize with proper structure
      let initialTechs: string[] = [];
      let initialActiveTech: string = '';
      let initialTechFormData: Record<string, FormData> = {};
      let initialCurrentFormData: FormData;
      let initialOutputs: OutputForm[] = [];

      if (currentScenarioData?.formData) {
        // Load existing current scenario data
        console.log('Loading existing current scenario data');
        initialCurrentFormData = currentScenarioData.formData;
        initialOutputs = currentScenarioData.outputs || [];
        initialTechs = currentScenarioData.technologies || [technologyName || "Technology 1"];
        initialActiveTech = initialTechs[0];

        // CRITICAL: Ensure technologyFormData is properly structured
        if (currentScenarioData.technologyFormData) {
          initialTechFormData = { ...currentScenarioData.technologyFormData };
        } else {
          // Create technology form data from current form data
          initialTechs.forEach((tech: string) => {
            initialTechFormData[tech] = {
              ...getDefaultFormData(activityName, tech),
              ...initialCurrentFormData,
              technology: tech,
              activity: activityName
            };
          });
        }
      } else if (baseScenarioData && !isNewNode) {
        // Pre-populate with base scenario data
        console.log('Pre-populating with base scenario data');
        console.log('Base scenario data structure:', baseScenarioData);

        // Use technology-specific form data if available, otherwise fall back to general formData
        let baseFormDataToUse = baseScenarioData.formData || {};
        if (baseScenarioData.technologyFormData && technologyName && baseScenarioData.technologyFormData[technologyName]) {
          baseFormDataToUse = baseScenarioData.technologyFormData[technologyName];
          console.log('Using technology-specific base form data for:', technologyName, baseFormDataToUse);
        } else {
          console.log('Using general base form data:', baseFormDataToUse);
        }

        initialCurrentFormData = {
          ...getDefaultFormData(activityName, technologyName),
          ...baseFormDataToUse,
          activity: activityName,
          technology: technologyName
        };
        initialOutputs = baseScenarioData.outputs ? [...baseScenarioData.outputs] : [];
        console.log('Base scenario outputs:', baseScenarioData.outputs);
        console.log('Initial outputs set to:', initialOutputs);
        if (initialOutputs.length > 0) {
          console.log('First output material outputs:', initialOutputs[0].matOutputs);
          console.log('Material output names:', initialOutputs[0].matOutputs?.map(m => m.material));
        }
        initialTechs = baseScenarioData.technologies || [technologyName || "Technology 1"];
        initialActiveTech = initialTechs[0];

        // Initialize technology form data from base scenario
        const baseTechFormData = baseScenarioData.technologyFormData || {};
        initialTechs.forEach((tech: string) => {
          const existingTechData = baseTechFormData[tech] || {};
          console.log('=== CURRENT SCENARIO INITIALIZATION DEBUG ===');
          console.log('Tech:', tech);
          console.log('Existing tech data:', existingTechData);
          console.log('Material inputs from base:', existingTechData.materialInputs);

          // CRITICAL FIX: Ensure base scenario data takes precedence over defaults
          const defaultData = getDefaultFormData(activityName, tech);
          initialTechFormData[tech] = {
            ...defaultData,
            ...existingTechData,
            // Ensure arrays from base scenario override defaults completely
            materialInputs: existingTechData.materialInputs || defaultData.materialInputs,
            energyInputs: existingTechData.energyInputs || defaultData.energyInputs,
            emissions: existingTechData.emissions || defaultData.emissions,
            technology: tech,
            activity: activityName
          };

          console.log('Final tech form data:', initialTechFormData[tech]);
          console.log('Final material inputs:', initialTechFormData[tech].materialInputs);
          console.log('Material input names:', initialTechFormData[tech].materialInputs?.map(m => m.material));
          console.log('=== END CURRENT SCENARIO INITIALIZATION DEBUG ===');
        });
      } else {
        // Initialize with default data for new nodes or when no base scenario
        console.log('Initializing with default form data');
        initialTechs = [technologyName || "Technology 1"];
        initialActiveTech = initialTechs[0];
        initialCurrentFormData = getDefaultFormData(activityName, technologyName);
        initialOutputs = [];

        // Initialize technology form data for new nodes
        initialTechs.forEach((tech: string) => {
          initialTechFormData[tech] = {
            ...getDefaultFormData(activityName, tech),
            technology: tech,
            activity: activityName
          };
        });
      }

      // CRITICAL FIX: Set all state synchronously to prevent rendering issues
      console.log('Setting initial state synchronously...');
      console.log('- initialTechs:', initialTechs);
      console.log('- initialActiveTech:', initialActiveTech);
      console.log('- initialTechFormData keys:', Object.keys(initialTechFormData));

      setTechnologies(initialTechs);
      setActiveTechnology(initialActiveTech);
      setTechnologyFormData(initialTechFormData);
      setCurrentFormData(initialCurrentFormData);
      setOutputs(initialOutputs);
      console.log('Outputs state set to:', initialOutputs);

      console.log('Initial state set successfully');
    }
  }, [open, currentScenarioData, baseScenarioData, isNewNode, activityName, technologyName]);

  // Sync currentFormData when activeTechnology changes
  useEffect(() => {
    if (open && activeTechnology && technologyFormData[activeTechnology]) {
      console.log('Syncing currentFormData for activeTechnology:', activeTechnology);
      setCurrentFormData(technologyFormData[activeTechnology]);
    }
  }, [open, activeTechnology, technologyFormData]);



  // Technology-specific form data management
  const getCurrentTechnologyFormData = (): FormData => {
    // Return existing data or fallback to currentFormData
    const techData = technologyFormData[activeTechnology];
    if (techData) {
      console.log('getCurrentTechnologyFormData: Using tech-specific data for', activeTechnology);
      console.log('Tech data material inputs:', techData.materialInputs);
      return techData;
    }

    // Fallback to currentFormData if no technology-specific data exists
    console.log('getCurrentTechnologyFormData: Using currentFormData as fallback for', activeTechnology);
    console.log('CurrentFormData material inputs:', currentFormData.materialInputs);
    return currentFormData;
  };

  // Debug effect to track form data changes
  useEffect(() => {
    if (open) {
      console.log('=== FORM DATA DEBUG ===');
      console.log('- activeTechnology:', activeTechnology);
      console.log('- currentFormData keys:', Object.keys(currentFormData));
      console.log('- technologyFormData keys:', Object.keys(technologyFormData));
      console.log('- currentFormData.activity:', currentFormData.activity);
      console.log('- currentFormData.technology:', currentFormData.technology);
      console.log('- currentFormData.energyInputs length:', currentFormData.energyInputs?.length);
      console.log('- currentFormData.materialInputs length:', currentFormData.materialInputs?.length);
      console.log('- currentFormData.emissions length:', currentFormData.emissions?.length);
      console.log('- activeFormTab:', activeFormTab);
      console.log('=======================');
    }
  }, [open, activeTechnology, currentFormData, technologyFormData, activeFormTab]);

  // Helper functions for form steps (exact copy from ConnectionFormDialog)
  const updateTechnologyFormData = (field: string, value: any) => {
    const currentData = getCurrentTechnologyFormData();
    const updatedData = {
      ...currentData,
      [field]: value
    };

    // Update technology form data
    setTechnologyFormData(prev => ({
      ...prev,
      [activeTechnology]: updatedData
    }));

    // Also update currentFormData to keep it in sync
    setCurrentFormData(updatedData);

    console.log('updateTechnologyFormData:', field, '=', value, 'for', activeTechnology);
  };

  const updateTechnologyFormField = (section: string, field: string, value: string) => {
    const currentData = getCurrentTechnologyFormData();
    const currentSection = currentData[section as keyof FormData] as any;

    const updatedData = {
      ...currentData,
      [section]: {
        ...currentSection,
        [field]: value
      }
    };

    // Update technology form data
    setTechnologyFormData(prev => ({
      ...prev,
      [activeTechnology]: updatedData
    }));

    // Also update currentFormData to keep it in sync
    setCurrentFormData(updatedData);

    console.log('updateTechnologyFormField:', section, field, '=', value, 'for', activeTechnology);
  };

  const addNewTechnology = (customTechName?: string) => {
    const newTechName = customTechName || `Technology ${technologies.length + 1}`;

    // CRITICAL FIX: Check if technology already exists to prevent duplicates
    if (technologies.includes(newTechName)) {
      console.log('Technology already exists:', newTechName);
      setActiveTechnology(newTechName); // Just switch to existing technology
      return;
    }

    console.log('Adding new technology:', newTechName);
    setTechnologies(prev => [...prev, newTechName]);
    setActiveTechnology(newTechName);

    // Initialize form data for new technology
    const newTechFormData = getDefaultFormData(activityName, newTechName);
    setTechnologyFormData(prev => ({
      ...prev,
      [newTechName]: newTechFormData
    }));

    // Also update currentFormData to ensure form renders immediately
    setCurrentFormData(newTechFormData);
  };

  const updateTechnologyName = (oldName: string, newName: string) => {
    setTechnologies(prev => prev.map(t => t === oldName ? newName : t));
    if (activeTechnology === oldName) setActiveTechnology(newName);

    // Update technology form data key
    if (technologyFormData[oldName]) {
      setTechnologyFormData(prev => {
        const newData = { ...prev };
        newData[newName] = { ...newData[oldName], technology: newName };
        delete newData[oldName];
        return newData;
      });
    }
  };

  const updateOutputField = (fieldPath: string, value: any) => {
    // Parse the field path (e.g., "outputs.0.energyOutputs.0.energy")
    const pathParts = fieldPath.split('.');

    if (pathParts[0] === 'outputs') {
      const outputIndex = parseInt(pathParts[1]);
      const updatedOutputs = [...outputs];

      if (pathParts.length === 3) {
        // Direct output field (e.g., "outputs.0.targetNode")
        updatedOutputs[outputIndex] = {
          ...updatedOutputs[outputIndex],
          [pathParts[2]]: value
        };
      } else if (pathParts.length === 5) {
        // Nested field (e.g., "outputs.0.energyOutputs.0.energy")
        const arrayField = pathParts[2] as 'energyOutputs' | 'matOutputs';
        const itemIndex = parseInt(pathParts[3]);
        const field = pathParts[4];

        updatedOutputs[outputIndex] = {
          ...updatedOutputs[outputIndex],
          [arrayField]: updatedOutputs[outputIndex][arrayField].map((item: any, idx: number) =>
            idx === itemIndex ? { ...item, [field]: value } : item
          )
        };
      }

      setOutputs(updatedOutputs);
    }
  };

  const clearError = (errorKey: string) => {
    const newErrors = { ...errors };
    delete newErrors[errorKey];
    setErrors(newErrors);
  };







  const handleSave = () => {
    console.log('DualTabNodeEditor handleSave called');
    console.log('- currentFormData:', currentFormData);
    console.log('- outputs:', outputs);
    console.log('- technologies:', technologies);
    console.log('- technologyFormData:', technologyFormData);

    const completeFormData = {
      formData: currentFormData,
      outputs: outputs,
      technologies: technologies,
      technologyFormData: technologyFormData
    };

    console.log('Calling parent onSave with:', completeFormData);
    onSave(completeFormData);
    onClose();
  };



  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Edit3 size={20} />
            Edit Node: {activityName}
            {technologyName && (
              <Badge variant="outline" className="ml-2">
                {technologyName}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {/* Higher-level tabs for Base vs Current Scenario */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'base' | 'current')} className="flex-1 flex flex-col overflow-hidden min-h-0">
          <TabsList className="grid w-full grid-cols-2 mb-4 flex-shrink-0">
            <TabsTrigger value="base" className="flex items-center gap-2">
              <Lock size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{baseScenarioName}</span>
                <span className="text-xs text-muted-foreground">Base Scenario</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="current" className="flex items-center gap-2">
              <Edit3 size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{currentScenarioName}</span>
                <span className="text-xs text-muted-foreground">Current Scenario</span>
              </div>
            </TabsTrigger>
          </TabsList>

          {/* Base Scenario Tab - Read-only form */}
          <TabsContent value="base" className="flex-1 overflow-hidden min-h-0">
            {isNewNode ? (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This is a new node that doesn't exist in the base scenario.
                  No base scenario data is available for comparison.
                </AlertDescription>
              </Alert>
            ) : baseScenarioData ? (
              <div className="h-full flex flex-col overflow-hidden">
                <Alert className="mx-4 mb-4 flex-shrink-0">
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    This is the read-only data from the base scenario. Use the "Current Scenario" tab to make changes.
                  </AlertDescription>
                </Alert>

                {/* Read-only form display using ConnectionForm */}
                <div className="flex-1 overflow-y-auto px-4 pb-4">
                  {/* Technology Name Header */}
                  <div className="mb-4 flex-shrink-0">
                    <h3 className="text-lg font-semibold text-gray-900">{technologyName || 'Technology'}</h3>
                  </div>
                  <ConnectionForm
                    key={`base-scenario-${nodeId}`}
                    formData={(() => {
                      // Use technology-specific form data if available, otherwise fall back to general formData
                      if (baseScenarioData?.technologyFormData && technologyName && baseScenarioData.technologyFormData[technologyName]) {
                        const techFormData = baseScenarioData.technologyFormData[technologyName];
                        console.log('Base scenario using technology-specific form data for', technologyName, ':', techFormData);
                        console.log('Material inputs:', techFormData.materialInputs);
                        console.log('Financial data:', techFormData.financial);
                        return techFormData;
                      }
                      const generalFormData = baseScenarioData?.formData || {};
                      console.log('Base scenario using general form data:', generalFormData);
                      return generalFormData;
                    })()}
                    onFormDataChange={() => {}}
                    onSave={() => {}}
                    onCancel={() => {}}
                    activityName={activityName}
                    technologyName={technologyName}
                    readOnly={true}
                    showSaveButton={false}
                    initialOutputs={baseScenarioData?.outputs}
                    initialTechnologies={baseScenarioData?.technologies}
                    availableNodes={availableNodes}
                  />
                </div>
              </div>
            ) : (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  No base scenario data available for this node.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          {/* Current Scenario Tab - Editable form using ConnectionForm */}
          <TabsContent value="current" className="flex-1 overflow-hidden min-h-0">
            <div className="h-full flex flex-col overflow-hidden">
              <Alert className="mx-4 mb-4 flex-shrink-0">
                <Edit3 className="h-4 w-4" />
                <AlertDescription>
                  Make your changes here. This data will be saved to the current scenario.
                  {!isNewNode && baseScenarioData && " The form is pre-populated with base scenario data."}
                </AlertDescription>
              </Alert>

              {/* Tab-based form (like ConnectionForm) */}
              <div className="flex-1 overflow-hidden px-4 pb-4">
                <Tabs value={activeFormTab} onValueChange={setActiveFormTab} className="h-full flex flex-col">
                  {/* Technology Name Header */}
                  <div className="mb-4 flex-shrink-0">
                    <h3 className="text-lg font-semibold text-gray-900">{technologyName || 'Technology'}</h3>
                  </div>

                  <TabsList className="grid w-full grid-cols-4 mb-4 flex-shrink-0">
                    <TabsTrigger value="inputs">Inputs</TabsTrigger>
                    <TabsTrigger value="outputs">Outputs</TabsTrigger>
                    <TabsTrigger value="byproducts">By-products</TabsTrigger>
                    <TabsTrigger value="financial">Financial</TabsTrigger>
                  </TabsList>

                  <TabsContent value="inputs" className="flex-1 overflow-y-auto space-y-4">
                    <InputsStep
                      formData={getCurrentTechnologyFormData()}
                      updateField={updateTechnologyFormData}
                      updateFormField={updateTechnologyFormField}
                      errors={errors}
                      usingManualEntry={false}
                      energyInputAutoFillLabel=""
                      matInputAutoFillLabel=""
                      technologyAutoFillLabel={activeTechnology}
                      availableTechnologies={technologies}
                      availableNodes={availableNodes}
                      technologies={technologies}
                      activeTechnology={activeTechnology}
                      setActiveTechnology={setActiveTechnology}
                      onAddTechnology={addNewTechnology}
                      updateTechnologyName={updateTechnologyName}
                      readOnly={false}
                      apiTechnologies={apiData.technologies.technologies}
                      apiMaterials={apiData.materials.materials}
                      apiEnergies={apiData.energies.energies}
                      apiEmissions={apiData.emissions.emissions}
                      isLoadingApiData={apiData.isLoading}
                      onCreateTechnology={apiData.technologies.addTechnology}
                    />
                  </TabsContent>

                  <TabsContent value="outputs" className="flex-1 overflow-y-auto space-y-4">
                    <OutputsStep
                      outputs={outputs}
                      setOutputs={setOutputs}
                      activeOutputTab={activeOutputTab}
                      setActiveOutputTab={setActiveOutputTab}
                      errors={errors}
                      availableNodes={availableNodes}
                      updateOutputField={updateOutputField}
                      clearError={clearError}
                      technologies={technologies}
                      activeTechnology={activeTechnology}
                      setActiveTechnology={setActiveTechnology}
                      readOnly={false}
                      apiMaterials={apiData.materials.materials}
                      apiEnergies={apiData.energies.energies}
                      isLoadingApiData={apiData.isLoading}
                    />
                  </TabsContent>

                  <TabsContent value="byproducts" className="flex-1 overflow-y-auto space-y-4">
                    <ByProductsStep
                      formData={getCurrentTechnologyFormData()}
                      updateField={updateTechnologyFormData}
                      updateFormField={updateTechnologyFormField}
                      errors={errors}
                      technologies={technologies}
                      activeTechnology={activeTechnology}
                      setActiveTechnology={setActiveTechnology}
                      availableNodes={[]}
                      technologyAutoFillLabel={activeTechnology}
                      availableTechnologies={technologies}
                      onAddTechnology={addNewTechnology}
                      readOnly={false}
                      apiTechnologies={apiData.technologies.technologies}
                      apiMaterials={apiData.materials.materials}
                      apiEnergies={apiData.energies.energies}
                      apiEmissions={apiData.emissions.emissions}
                      isLoadingApiData={apiData.isLoading}
                    />
                  </TabsContent>

                  <TabsContent value="financial" className="flex-1 overflow-y-auto space-y-4">
                    <FinancialStep
                      formData={getCurrentTechnologyFormData()}
                      updateFormField={updateTechnologyFormField}
                      errors={errors}
                      outputs={outputs}
                      availableNodes={[]}
                      technologies={technologies}
                      readOnly={false}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600 text-white">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
