
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OutputTabs } from "./OutputTabs";
import {
  OutputForm,
  techs,
  EnergyOutput,
  MaterialOutput,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";
import { AddResourceButton } from './AddResourceButton';

interface OutputsStepProps {
  outputs: OutputForm[];
  setOutputs: React.Dispatch<React.SetStateAction<OutputForm[]>>;
  activeOutputTab: string;
  setActiveOutputTab: React.Dispatch<React.SetStateAction<string>>;
  errors: Record<string, string>;
  availableNodes: any[];
  updateOutputField: (fieldPath: string, value: any) => void;
  clearError?: (errorKey: string) => void;
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  onAddTechnology?: () => void;
  readOnly?: boolean;
  // API data props
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
  sectorUuid?: string;
  onMaterialAdded?: (material: MaterialResponse) => void;
  onEnergyAdded?: (energy: EnergyResponse) => void;
}

export const OutputsStep: React.FC<OutputsStepProps> = ({
  outputs,
  setOutputs,
  activeOutputTab,
  setActiveOutputTab,
  errors,
  availableNodes,
  updateOutputField,
  clearError,
  technologies,
  activeTechnology,
  setActiveTechnology,
  onAddTechnology,
  readOnly = false,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false,
  sectorUuid,
  onMaterialAdded,
  onEnergyAdded
}) => {
  // Track all custom technologies across all output tabs
  const [customTechnologies, setCustomTechnologies] = useState<Set<string>>(new Set());
  




  // Watch for changes in output technology and update tab labels
  useEffect(() => {
    const newCustomTechs = new Set<string>();
    
    outputs.forEach(output => {
      if (output.outputTechnology && !techs.includes(output.outputTechnology)) {
        newCustomTechs.add(output.outputTechnology);
      }
      

    });
    
    // Update custom technologies set if needed
    if (newCustomTechs.size !== customTechnologies.size || 
        [...newCustomTechs].some(tech => !customTechnologies.has(tech))) {
      setCustomTechnologies(newCustomTechs);
    }
    
    // Log energy and material byproducts if they exist in form data
    try {
      // Access the parent form data (if available) through a parent effect
      const parentElement = document.querySelector('form');
      if (parentElement) {
        const formData = new FormData(parentElement as HTMLFormElement);
        const energyByProducts = formData.get('energyByProducts');
        const materialByProducts = formData.get('materialByProducts');
        

      }
    } catch (error) {
      // Unable to access byproducts from form
    }
  }, [outputs, customTechnologies]);

  // Get tab label based on output data
  const getTabLabel = (output: OutputForm, index: number) => {
    if (output.outputTechnology) {
      return output.outputTechnology;
    }
    
    // If no technology is selected, use "Technology X" format
    return `Technology ${index + 1}`;
  };

  // Create an extended version of the updateOutputField function
  const handleOutputFieldUpdate = (outputId: string, fieldPath: string, value: any) => {
    updateOutputField(fieldPath, value);
    
    // If this is a technology update, add it to our custom technologies set if needed
    if (fieldPath === 'outputTechnology' && value && !techs.includes(value)) {
      setCustomTechnologies(prev => new Set([...prev, value]));
    }
  };

  return (
    <div className={readOnly ? "opacity-75" : ""}>
      <Tabs value={activeTechnology} onValueChange={readOnly ? undefined : setActiveTechnology} className="w-full">
        <TabsList className="mb-2 w-full overflow-x-auto flex whitespace-nowrap">
          {technologies.map((tech) => (
            <TabsTrigger
              key={tech}
              value={tech}
              className={`min-w-[100px] flex-shrink-0 ${readOnly ? 'cursor-not-allowed opacity-70' : ''}`}
              disabled={readOnly}
            >
              {tech}
            </TabsTrigger>
          ))}
        </TabsList>

        {technologies.map((tech) => (
          <TabsContent key={tech} value={tech}>
            {renderTechnologyContent(tech)}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  function renderTechnologyContent(technology: string) {
    // Find the output that corresponds to this technology
    let output = outputs.find(o => o.outputTechnology === technology);

    // If no exact match, try to find any output (fallback for data inconsistencies)
    if (!output && outputs.length > 0) {
      output = outputs[0];
      // Update the output technology to match the current technology
      output = { ...output, outputTechnology: technology };
    }

    if (!output) {
      return (
        <div className="text-center text-gray-500 py-4">
          No output configuration found for {technology}
        </div>
      );
    }

    return (
      <>
        <OutputTabs
          output={output}
          errors={errors}
          updateOutputField={(field, value) => {
            if (!readOnly) handleOutputFieldUpdate(output.id, field, value);
          }}
          clearError={clearError}
          availableNodes={availableNodes}
          apiTechnologies={apiTechnologies}
          apiMaterials={apiMaterials}
          apiEnergies={apiEnergies}
          apiEmissions={apiEmissions}
          isLoadingApiData={isLoadingApiData}
          readOnly={readOnly}
          sectorUuid={sectorUuid}
          onMaterialAdded={onMaterialAdded}
          onEnergyAdded={onEnergyAdded}
        />
      </>
    );
  }
};
